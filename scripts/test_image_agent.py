import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app'))

from app.agent.global_agent import run_agent_stream


async def test_image_agent():
    """
    Test function to run the agent with image attachments and print chunks
    """
    message = "what can you see in these images"
    attachments = [
        {
            "file_name": "annie-spratt-xpkTvptEbRw-unsplash Medium.jpeg",
            "file_type": "image/jpeg",
            "file_size": 68733,
            "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758521759-1758521758243-annie-spratt-xpkTvptEbRw-unsplashMedium.jpeg"
        },
        {
            "file_name": "raphael-maksian-Qn7bJt56sGY-unsplash Medium.jpeg",
            "file_type": "image/jpeg",
            "file_size": 112402,
            "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758521761-1758521761338-rapha<PERSON>-maksian-Qn7bJt56sGY-unsplashMedium.jpeg"
        }
    ]

    print(f"Running agent with message: {message}")
    print(f"With {len(attachments)} attachments")
    print("-" * 50)

    async for chunk in run_agent_stream(message, attachments=attachments):
        print(chunk)

    print("\n" + "-" * 50)
    print("Agent response completed")


if __name__ == "__main__":
    asyncio.run(test_image_agent())