import math
from typing import Dict, List, Optional, <PERSON><PERSON>

import numpy as np


def calculate_repulsive_force(
    rect1: Dict, rect2: Dict, separation: float, strength: float
) -> Tu<PERSON>[float, float]:
    """
    Calculate repulsive force between two rectangles.

    Args:
        rect1, rect2: Rectangle dictionaries with 'x', 'y', 'w', 'h'
        separation: Minimum separation distance
        strength: Force strength multiplier

    Returns:
        Tuple of (force_x, force_y) applied to rect1
    """
    # Calculate centers
    center1_x = rect1["x"] + rect1["w"] / 2
    center1_y = rect1["y"] + rect1["h"] / 2
    center2_x = rect2["x"] + rect2["w"] / 2
    center2_y = rect2["y"] + rect2["h"] / 2

    # Calculate distance between centers
    dx = center1_x - center2_x
    dy = center1_y - center2_y
    distance = math.sqrt(dx**2 + dy**2)

    if distance < 1e-6:  # Very small distance, avoid division by zero
        # Apply small random displacement
        dx = np.random.uniform(-1, 1)
        dy = np.random.uniform(-1, 1)
        distance = 1.0

    # Calculate minimum distance needed based on rectangle dimensions
    min_distance_x = (rect1["w"] + rect2["w"]) / 2 + separation
    min_distance_y = (rect1["h"] + rect2["h"]) / 2 + separation

    # Use the larger of the two minimum distances to ensure proper separation
    min_distance = max(min_distance_x, min_distance_y)

    # Only apply force if rectangles are too close
    if distance < min_distance:
        # Calculate force magnitude - use a more controlled approach
        overlap = min_distance - distance
        force_magnitude = strength * overlap

        # Normalize direction vector
        dx_norm = dx / distance
        dy_norm = dy / distance

        # Calculate force components
        force_x = force_magnitude * dx_norm
        force_y = force_magnitude * dy_norm

        return force_x, force_y

    return 0.0, 0.0


def rectangle_layout_with_separation(
    rectangles: List[Dict],
    fixed_node_id: Optional[str] = None,
    separation: float = 10.0,
    max_iterations: int = 1000,
    convergence_threshold: float = 0.1,
    force_strength: float = 0.1,
    damping: float = 0.8,
    step_size: float = 1.0,
) -> List[Dict]:
    """
    Arrange rectangles with no overlap and fixed separation using force-directed layout.

    Args:
        rectangles: List of dictionaries with keys 'id', 'x', 'y', 'w', 'h'
        fixed_node_id: ID of the node to keep fixed in position (optional)
        separation: Minimum separation distance between rectangles
        max_iterations: Maximum number of iterations for the algorithm
        convergence_threshold: Threshold for convergence (stop when movement is small)
        force_strength: Strength of repulsive forces (smaller values for gentler movement)
        damping: Damping factor to reduce oscillations
        step_size: Maximum step size per iteration

    Returns:
        List of rectangles with updated positions
    """
    if not rectangles:
        return rectangles

    # Create a copy to avoid modifying the original
    nodes = [rect.copy() for rect in rectangles]

    # Find the fixed node index
    fixed_index = None
    if fixed_node_id:
        for i, node in enumerate(nodes):
            if node["id"] == fixed_node_id:
                fixed_index = i
                break

    # Initialize velocities
    velocities = [[0.0, 0.0] for _ in nodes]

    for iteration in range(max_iterations):
        forces = [[0.0, 0.0] for _ in nodes]
        max_movement = 0.0

        # Calculate repulsive forces between all pairs of rectangles
        for i in range(len(nodes)):
            for j in range(i + 1, len(nodes)):
                force_x, force_y = calculate_repulsive_force(
                    nodes[i], nodes[j], separation, force_strength
                )

                forces[i][0] += force_x
                forces[i][1] += force_y
                forces[j][0] -= force_x
                forces[j][1] -= force_y

        # Update positions based on forces
        for i in range(len(nodes)):
            if fixed_index is not None and i == fixed_index:
                continue  # Skip the fixed node

            # Update velocity with damping
            velocities[i][0] = velocities[i][0] * damping + forces[i][0]
            velocities[i][1] = velocities[i][1] * damping + forces[i][1]

            # Limit step size to prevent explosive movement
            velocity_magnitude = math.sqrt(
                velocities[i][0] ** 2 + velocities[i][1] ** 2
            )
            if velocity_magnitude > step_size:
                velocities[i][0] = (velocities[i][0] / velocity_magnitude) * step_size
                velocities[i][1] = (velocities[i][1] / velocity_magnitude) * step_size

            # Update position
            old_x, old_y = nodes[i]["x"], nodes[i]["y"]
            nodes[i]["x"] += velocities[i][0]
            nodes[i]["y"] += velocities[i][1]

            # Calculate movement for convergence check
            movement = math.sqrt(
                (nodes[i]["x"] - old_x) ** 2 + (nodes[i]["y"] - old_y) ** 2
            )
            max_movement = max(max_movement, movement)

        # Check for convergence
        if max_movement < convergence_threshold:
            break

    return nodes
