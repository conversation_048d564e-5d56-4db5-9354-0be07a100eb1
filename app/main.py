import asyncio
import os

from app.shared.config.logging_config import get_logger, setup_logging
from app.services.redis_listener import listen_event_from_redis_pubsub
from app.shared.config.base import get_settings

# Determine if we should use JSON logging format
use_json = os.getenv("LOG_FORMAT", "").lower() == "json"

# Set up logging before anything else
setup_logging(
    default_level=os.getenv("LOG_LEVEL", "DEBUG,INFO,ERROR"),
    logs_dir=os.getenv("LOGS_DIR", "logs"),
    use_json=use_json,
)

# Get logger for this module
logger = get_logger(__name__)

# Get configuration
settings = get_settings()

# Log application startup
logger.info(
    "Application starting",
    extra={"environment": settings.environment},
)


def main():
    try:
        logger.info("Starting Redis listener")
        asyncio.run(listen_event_from_redis_pubsub())
    except KeyboardInterrupt:
        logger.warning("Shutting down due to keyboard interrupt")
    except Exception as e:
        logger.error(
            "Error occurred in Redis listener", exc_info=True, extra={"error": str(e)}
        )


if __name__ == "__main__":
    """
    Usage:
        To run the application:
        poetry run python -m app.main
    """
    main()

