import asyncio
from mcp.client.streamable_http import streamablehttp_client
from strands import Agent
from strands.tools.mcp.mcp_client import MC<PERSON><PERSON>
from pydantic import BaseModel, Field
from typing import List

import os

from dotenv import load_dotenv
from app.strands.agents.web_search_prompt import WEB_SEARCH_PROMPT
from app.strands.model_config import get_default_model
from app.strands.utils import format_strands_chunk

load_dotenv()

exa_api_key = os.getenv("EXA_API_KEY")

if not exa_api_key:
    raise ValueError("Missing EXA_API_KEY environment variable")

exa_mcp_url = f"https://mcp.exa.ai/mcp?exaApiKey={exa_api_key}"

streamable_http_mcp_client = MCPClient(lambda: streamablehttp_client(exa_mcp_url))

# Get the default model configuration
model = get_default_model()


class Source(BaseModel):
    """Individual source information."""

    title: str = Field(description="The title of the web page or article")
    url: str = Field(description="The URL link to the web page")


class Segment(BaseModel):
    """Text segment with source reference."""

    startIndex: int = Field(
        description="Start character index of the segment in content"
    )
    endIndex: int = Field(description="End character index of the segment in content")
    sourceIndex: int = Field(description="Index of the source in the sources array")


class WebSearchResponse(BaseModel):
    """Complete web search response with structured results."""

    sources: List[Source] = Field(
        default_factory=list, description="List of sources found during search"
    )
    content: str = Field(description="Clean content without inline citations")
    segments: List[Segment] = Field(
        default_factory=list, description="Text segments mapping to sources"
    )


async def run_agent():
    # Create an agent with MCP tools within the context manager
    with streamable_http_mcp_client:
        # Get the tools from the MCP server
        tools = streamable_http_mcp_client.list_tools_sync()

        # Create an agent with these tools
        agent = Agent(tools=tools, model=model, system_prompt=WEB_SEARCH_PROMPT)

        print("Agent is ready! Type your message (or 'quit' to exit):")

        while True:
            try:
                user_input = input("\n> ").strip()

                if user_input.lower() in ["quit", "exit", "q"]:
                    print("Goodbye!")
                    break

                if not user_input:
                    print("Please enter a message.")
                    continue

                print("\nAgent is processing your request...\n")

                # Use streaming output for async chunks
                try:

                    result = agent.stream_async(user_input)

                    async for chunk in result:
                        formatted_chunk = format_strands_chunk(chunk)
                        if formatted_chunk:
                            print(formatted_chunk)

                except Exception as e:
                    print(f"Streaming failed, falling back to structured output: {e}")
                    result = await agent.structured_output_async(
                        WebSearchResponse, user_input
                    )
                    print(result)

            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")
                print("Please try again.")


if __name__ == "__main__":
    asyncio.run(run_agent())
