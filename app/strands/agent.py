import asyncio
from strands import Agent

from app.services.model_config import get_default_model
from strands_tools import python_repl, calculator

from app.strands.utils import format_strands_chunk
from app.strands.tools.todo import todo_agent_tool

model = get_default_model()

# agent = Agent(tools=[todo_read, todo_write], model=model)
agent = Agent(tools=[python_repl, calculator, todo_agent_tool], model=model)


async def run_agent():
    print("Agent is ready! Type your message (or 'quit' to exit):")

    while True:
        try:
            user_input = input("\n> ").strip()

            if user_input.lower() in ["quit", "exit", "q"]:
                print("Goodbye!")
                break

            if not user_input:
                print("Please enter a message.")
                continue

            print("\nAgent is processing your request...\n")
            # response = agent.stream_async(user_input)
            response = agent.stream_async(user_input)
            async for event in response:
                formatted_event = format_strands_chunk(event)
                if formatted_event:
                    print(formatted_event)

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
            print("Please try again.")


if __name__ == "__main__":
    asyncio.run(run_agent())
