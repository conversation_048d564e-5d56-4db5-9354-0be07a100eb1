import json
import logging
from typing import Any, Dict, List, Optional

import redis.asyncio as redis

from ..shared.config.base import get_settings


class RedisClient:
    def __init__(self):
        self.settings = get_settings()
        self.client = None
        self._connection_initialized = False

    async def initialize(self):
        """Initialize Redis connection asynchronously"""
        if not self._connection_initialized:
            self.client = redis.Redis(
                host=self.settings.redis.redis_host,
                port=self.settings.redis.redis_port,
                db=self.settings.redis.redis_db,
                password=self.settings.redis.password,
                decode_responses=True,
            )
            self._connection_initialized = True
        return self

    def serialize_value(self, value: Any) -> str:
        """Serialize value to string format suitable for Redis storage"""
        if isinstance(value, (dict, list)):
            return json.dumps(value)
        return str(value)

    def deserialize_value(self, value: str, default_type: Any = None) -> Any:
        """Deserialize value from Redis storage"""
        if not value:
            return default_type
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value

    async def set_hash(
        self, key: str, value: Dict[str, Any], ttl: Optional[int] = None
    ):
        """Set a hash in Redis with optional TTL"""
        try:
            await self._ensure_connection()
            # Serialize each value in the dictionary
            serialized_dict = {k: self.serialize_value(v) for k, v in value.items()}
            await self.client.hset(key, mapping=serialized_dict)
            if ttl:
                await self.client.expire(key, ttl)
            logging.info(f"Set Redis hash {key} with TTL {ttl}")
        except Exception as e:
            logging.error(f"Failed to set Redis hash {key}: {e}")
            raise

    async def get_hash(self, key: str) -> Dict[str, Any]:
        """Get a hash from Redis"""
        try:
            await self._ensure_connection()
            data = await self.client.hgetall(key)
            # Deserialize each value in the dictionary
            deserialized_dict = {k: self.deserialize_value(v) for k, v in data.items()}
            logging.info(f"Got Redis hash {key}")
            return deserialized_dict
        except Exception as e:
            logging.error(f"Failed to get Redis hash {key}: {e}")
            raise

    async def exists(self, key: str) -> bool:
        """Check if a key exists in Redis"""
        try:
            await self._ensure_connection()
            exists = await self.client.exists(key) == 1
            logging.info(f"Checked existence of key {key}: {exists}")
            return exists
        except Exception as e:
            logging.error(f"Failed to check existence of key {key}: {e}")
            raise

    async def delete(self, key: str):
        """Delete a key from Redis"""
        try:
            await self._ensure_connection()
            result = await self.client.delete(key)
            logging.info(f"Deleted key {key}")
            return result
        except Exception as e:
            logging.error(f"Failed to delete key {key}: {e}")
            raise

    async def set_value(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set a value in Redis with optional TTL"""
        try:
            await self._ensure_connection()
            serialized_value = self.serialize_value(value)
            await self.client.set(key, serialized_value)
            if ttl:
                await self.client.expire(key, ttl)
            logging.info(f"Set Redis key {key} with value and TTL {ttl}")
        except Exception as e:
            logging.error(f"Failed to set Redis key {key}: {e}")
            raise

    async def get_value(self, key: str) -> Optional[Any]:
        """Get a value from Redis"""
        try:
            await self._ensure_connection()
            value = await self.client.get(key)
            logging.info(f"Got Redis key {key}")
            return self.deserialize_value(value)
        except Exception as e:
            logging.error(f"Failed to get Redis key {key}: {e}")
            raise

    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment a value in Redis"""
        try:
            await self._ensure_connection()
            new_value = await self.client.incr(key, amount)
            logging.info(f"Incremented key {key} by {amount}")
            return new_value
        except Exception as e:
            logging.error(f"Failed to increment key {key}: {e}")
            raise

    async def rpush(self, key: str, *values: List[str]):
        """Push values to the right of a list"""
        try:
            await self._ensure_connection()
            result = await self.client.rpush(key, *values)
            logging.info(f"Pushed values to list {key}")
            return result
        except Exception as e:
            logging.error(f"Failed to push values to list {key}: {e}")
            raise

    async def set_list(self, key: str, values: list, ttl: Optional[int] = None):
        """Set a list in Redis with optional TTL"""
        try:
            await self._ensure_connection()
            await self.client.delete(key)  # Clear existing list
            if values:  # Only push if there are values
                serialized_values = [self.serialize_value(v) for v in values]
                await self.client.rpush(key, *serialized_values)
            if ttl:
                await self.client.expire(key, ttl)
            logging.info(f"Set Redis list {key} with TTL {ttl}")
        except Exception as e:
            logging.error(f"Failed to set Redis list {key}: {e}")
            raise

    async def get_list(self, key: str) -> list:
        """Get a list from Redis"""
        try:
            await self._ensure_connection()
            values = await self.client.lrange(key, 0, -1)
            deserialized_values = [self.deserialize_value(v) for v in values]
            logging.info(f"Got Redis list {key}")
            return deserialized_values
        except Exception as e:
            logging.error(f"Failed to get Redis list {key}: {e}")
            raise

    async def lrange(self, key: str, start: int, end: int) -> list:
        """Get a range of values from a list"""
        try:
            await self._ensure_connection()
            values = await self.client.lrange(key, start, end)
            logging.info(f"Got range from list {key}")
            return values
        except Exception as e:
            logging.error(f"Failed to get range from list {key}: {e}")
            raise

    async def publish(self, channel: str, message: str):
        """Publish a message to a channel"""
        try:
            await self._ensure_connection()
            await self.client.publish(channel, message)
            logging.info(f"Published message to channel {channel}")
        except Exception as e:
            logging.error(f"Failed to publish message to channel {channel}: {e}")
            raise

    async def subscribe(self, channel: str):
        """Subscribe to a channel"""
        try:
            await self._ensure_connection()
            pubsub = self.client.pubsub()
            await pubsub.subscribe(channel)
            logging.info(f"Subscribed to channel {channel}")
            return pubsub
        except Exception as e:
            logging.error(f"Failed to subscribe to channel {channel}: {e}")
            raise

    async def expire(self, key: str, ttl: int):
        """Set expiration time for a key"""
        try:
            await self._ensure_connection()
            result = await self.client.expire(key, ttl)
            logging.info(f"Set expiration for key {key} to {ttl} seconds")
            return result
        except Exception as e:
            logging.error(f"Failed to set expiration for key {key}: {e}")
            raise

    async def flushdb(self, async_flush: bool = True):
        """Flush the current database"""
        try:
            await self._ensure_connection()
            if async_flush:
                await self.client.flushdb(asynchronous=True)
                logging.info("Asynchronously flushed the current database")
            else:
                await self.client.flushdb(asynchronous=False)
                logging.info("Synchronously flushed the current database")
        except Exception as e:
            logging.error(f"Failed to flush database: {e}")
            raise

    async def wait(self, num_replicas: int, timeout: int):
        """Wait for replication to specified number of replicas"""
        try:
            await self._ensure_connection()
            result = await self.client.wait(num_replicas, timeout)
            logging.info(f"Waited for {result} replicas with timeout {timeout}ms")
            return result
        except Exception as e:
            logging.error(f"Failed to wait for replication: {e}")
            raise

    async def close(self):
        """Close Redis connection"""
        if self.client and self._connection_initialized:
            await self.client.close()
            self._connection_initialized = False
            logging.info("Closed Redis connection")

    async def hset(self, key: str, field: str, value: Any):
        """Set a field in a hash"""
        try:
            await self._ensure_connection()
            serialized_value = self.serialize_value(value)
            result = await self.client.hset(key, field, serialized_value)
            logging.info(f"Set field {field} in hash {key}")
            return result
        except Exception as e:
            logging.error(f"Failed to set field {field} in hash {key}: {e}")
            raise

    async def hincr(self, key: str, field: str, amount: int = 1) -> int:
        """Increment a field in a hash"""
        try:
            await self._ensure_connection()
            new_value = await self.client.hincrby(key, field, amount)
            logging.info(f"Incremented field {field} in hash {key} by {amount}")
            return new_value
        except Exception as e:
            logging.error(f"Failed to increment field {field} in hash {key}: {e}")
            raise

    async def scan(self, pattern: str, count: int = 100) -> List[str]:
        """Scan for keys matching a pattern"""
        try:
            await self._ensure_connection()
            keys = []
            cursor = 0
            while True:
                cursor, partial_keys = await self.client.scan(
                    cursor=cursor, match=pattern, count=count
                )
                keys.extend(partial_keys)
                if cursor == 0:
                    break
            logging.info(
                f"Scanned for keys matching pattern {pattern}, found {len(keys)} keys"
            )
            return keys
        except Exception as e:
            logging.error(f"Failed to scan for keys matching pattern {pattern}: {e}")
            raise

    async def set_binary(self, key: str, value: bytes, ttl: Optional[int] = None):
        """Set a binary value in Redis with optional TTL"""
        try:
            await self._ensure_connection()
            await self.client.set(key, value)
            if ttl:
                await self.client.expire(key, ttl)
            logging.info(f"Set Redis binary key {key} with TTL {ttl}")
        except Exception as e:
            logging.error(f"Failed to set Redis binary key {key}: {e}")
            raise

    async def get_binary(self, key: str) -> Optional[bytes]:
        """Get a binary value from Redis"""
        try:
            await self._ensure_connection()
            # Set decode_responses=False for this specific operation
            client = redis.Redis(
                host=self.settings.redis.redis_host,
                port=self.settings.redis.redis_port,
                db=self.settings.redis.redis_db,
                password=self.settings.redis.password,
                decode_responses=False,
            )
            value = await client.get(key)
            await client.close()
            logging.info(f"Got Redis binary key {key}")
            return value
        except Exception as e:
            logging.error(f"Failed to get Redis binary key {key}: {e}")
            raise

    async def _ensure_connection(self):
        """Ensure Redis connection is initialized"""
        if not self._connection_initialized:
            await self.initialize()
    
    async def publish_message(self, channel: str, message: str):
        """Publish a message to a channel"""
        try:
            await self._ensure_connection()
            await self.client.publish(channel, message)
            logging.info(f"Published message to channel {channel}")
        except Exception as e:
            logging.error(f"Failed to publish message to channel {channel}: {e}")
            raise

    # Redis Streams methods
    async def xadd(self, stream: str, fields: Dict[str, Any], message_id: str = "*", maxlen: Optional[int] = None):
        """Add a message to a Redis Stream"""
        try:
            await self._ensure_connection()
            # Serialize field values
            serialized_fields = {k: self.serialize_value(v) for k, v in fields.items()}

            kwargs = {}
            if maxlen is not None:
                kwargs['maxlen'] = maxlen
                kwargs['approximate'] = True  # Use approximate trimming for better performance

            message_id = await self.client.xadd(stream, serialized_fields, id=message_id, **kwargs)
            logging.info(f"Added message to stream {stream} with ID {message_id}")
            return message_id
        except Exception as e:
            logging.error(f"Failed to add message to stream {stream}: {e}")
            raise

    async def xread(self, streams: Dict[str, str], count: Optional[int] = None, block: Optional[int] = None):
        """Read messages from Redis Streams"""
        try:
            await self._ensure_connection()
            kwargs = {}
            if count is not None:
                kwargs['count'] = count
            if block is not None:
                kwargs['block'] = block

            result = await self.client.xread(streams, **kwargs)

            # Handle different return formats from redis-py
            if result is None:
                return {}

            # If result is a list, convert to dict format
            if isinstance(result, list):
                deserialized_result = {}
                for item in result:
                    if isinstance(item, (list, tuple)) and len(item) == 2:
                        stream_name, messages = item
                        deserialized_messages = []
                        for message_id, fields in messages:
                            deserialized_fields = {k: self.deserialize_value(v) for k, v in fields.items()}
                            deserialized_messages.append((message_id, deserialized_fields))
                        deserialized_result[stream_name] = deserialized_messages
                return deserialized_result

            # If result is already a dict, process normally
            elif isinstance(result, dict):
                deserialized_result = {}
                for stream_name, messages in result.items():
                    deserialized_messages = []
                    for message_id, fields in messages:
                        deserialized_fields = {k: self.deserialize_value(v) for k, v in fields.items()}
                        deserialized_messages.append((message_id, deserialized_fields))
                    deserialized_result[stream_name] = deserialized_messages

                logging.info(f"Read {sum(len(msgs) for msgs in deserialized_result.values())} messages from streams")
                return deserialized_result

            else:
                logging.warning(f"Unexpected result format from xread: {type(result)}")
                return {}
        except Exception as e:
            logging.error(f"Failed to read from streams: {e}")
            raise

    async def xreadgroup(self, group: str, consumer: str, streams: Dict[str, str],
                        count: Optional[int] = None, block: Optional[int] = None, noack: bool = False):
        """Read messages from Redis Streams using consumer groups"""
        try:
            await self._ensure_connection()
            kwargs = {}
            if count is not None:
                kwargs['count'] = count
            if block is not None:
                kwargs['block'] = block
            if noack:
                kwargs['noack'] = True

            result = await self.client.xreadgroup(group, consumer, streams, **kwargs)

            # Handle different return formats from redis-py
            if result is None:
                return {}

            # If result is a list, convert to dict format
            if isinstance(result, list):
                deserialized_result = {}
                for item in result:
                    if isinstance(item, (list, tuple)) and len(item) == 2:
                        stream_name, messages = item
                        deserialized_messages = []
                        for message_id, fields in messages:
                            deserialized_fields = {k: self.deserialize_value(v) for k, v in fields.items()}
                            deserialized_messages.append((message_id, deserialized_fields))
                        deserialized_result[stream_name] = deserialized_messages
                return deserialized_result

            # If result is already a dict, process normally
            elif isinstance(result, dict):
                deserialized_result = {}
                for stream_name, messages in result.items():
                    deserialized_messages = []
                    for message_id, fields in messages:
                        deserialized_fields = {k: self.deserialize_value(v) for k, v in fields.items()}
                        deserialized_messages.append((message_id, deserialized_fields))
                    deserialized_result[stream_name] = deserialized_messages

                logging.info(f"Read {sum(len(msgs) for msgs in deserialized_result.values())} messages from streams using group {group}")
                return deserialized_result

            else:
                logging.warning(f"Unexpected result format from xreadgroup: {type(result)}")
                return {}
        except Exception as e:
            logging.error(f"Failed to read from streams using group {group}: {e}")
            raise

    async def xack(self, stream: str, group: str, *message_ids):
        """Acknowledge messages in a Redis Stream consumer group"""
        try:
            await self._ensure_connection()
            result = await self.client.xack(stream, group, *message_ids)
            logging.info(f"Acknowledged {result} messages in stream {stream} for group {group}")
            return result
        except Exception as e:
            logging.error(f"Failed to acknowledge messages in stream {stream}: {e}")
            raise

    async def xgroup_create(self, stream: str, group: str, id: str = "0", mkstream: bool = False):
        """Create a consumer group for a Redis Stream"""
        try:
            await self._ensure_connection()
            await self.client.xgroup_create(stream, group, id=id, mkstream=mkstream)
            logging.info(f"Created consumer group {group} for stream {stream}")
        except Exception as e:
            # Ignore error if group already exists
            if "BUSYGROUP" in str(e):
                logging.info(f"Consumer group {group} already exists for stream {stream}")
            else:
                logging.error(f"Failed to create consumer group {group} for stream {stream}: {e}")
                raise

    async def xgroup_destroy(self, stream: str, group: str):
        """Destroy a consumer group for a Redis Stream"""
        try:
            await self._ensure_connection()
            result = await self.client.xgroup_destroy(stream, group)
            logging.info(f"Destroyed consumer group {group} for stream {stream}")
            return result
        except Exception as e:
            logging.error(f"Failed to destroy consumer group {group} for stream {stream}: {e}")
            raise

    async def xlen(self, stream: str):
        """Get the length of a Redis Stream"""
        try:
            await self._ensure_connection()
            length = await self.client.xlen(stream)
            logging.info(f"Stream {stream} has {length} messages")
            return length
        except Exception as e:
            logging.error(f"Failed to get length of stream {stream}: {e}")
            raise

    async def xtrim(self, stream: str, maxlen: int, approximate: bool = True):
        """Trim a Redis Stream to a maximum length"""
        try:
            await self._ensure_connection()
            result = await self.client.xtrim(stream, maxlen=maxlen, approximate=approximate)
            logging.info(f"Trimmed stream {stream} to {maxlen} messages, removed {result} messages")
            return result
        except Exception as e:
            logging.error(f"Failed to trim stream {stream}: {e}")
            raise

    async def xpending_range(self, stream: str, group: str, min_id: str = "-", max_id: str = "+",
                           count: int = 100, consumer: Optional[str] = None):
        """Get pending messages from a Redis Stream consumer group"""
        try:
            await self._ensure_connection()
            if consumer:
                result = await self.client.xpending_range(stream, group, min=min_id, max=max_id, count=count, consumer=consumer)
            else:
                result = await self.client.xpending_range(stream, group, min=min_id, max=max_id, count=count)
            return result
        except Exception as e:
            logging.error(f"Failed to get pending messages for stream {stream}, group {group}: {e}")
            return []

    async def xclaim(self, stream: str, group: str, consumer: str, min_idle_time: int, *message_ids):
        """Claim pending messages in a Redis Stream consumer group"""
        try:
            await self._ensure_connection()
            result = await self.client.xclaim(stream, group, consumer, min_idle_time, *message_ids)

            # Deserialize the results
            deserialized_result = []
            for message_id, fields in result:
                deserialized_fields = {k: self.deserialize_value(v) for k, v in fields.items()}
                deserialized_result.append((message_id, deserialized_fields))

            return deserialized_result
        except Exception as e:
            logging.error(f"Failed to claim messages for stream {stream}, group {group}: {e}")
            return []

    async def xinfo_stream(self, stream: str):
        """Get information about a Redis Stream"""
        try:
            await self._ensure_connection()
            result = await self.client.xinfo_stream(stream)
            return result
        except Exception as e:
            logging.error(f"Failed to get info for stream {stream}: {e}")
            return {}

    async def xinfo_groups(self, stream: str):
        """Get information about consumer groups for a Redis Stream"""
        try:
            await self._ensure_connection()
            result = await self.client.xinfo_groups(stream)
            return result
        except Exception as e:
            logging.error(f"Failed to get group info for stream {stream}: {e}")
            return []
