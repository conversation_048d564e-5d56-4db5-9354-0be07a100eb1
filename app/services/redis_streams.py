import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional, Tu<PERSON>, AsyncGenerator
from dataclasses import dataclass

from app.helper.redis_client import RedisClient
from app.shared.config.base import get_settings
from app.shared.config.constants import RedisStreamEnum


@dataclass
class StreamMessage:
    """Represents a message in a Redis Stream"""
    id: str
    fields: Dict[str, Any]
    stream: str
    timestamp: Optional[float] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class RedisStreamsProducer:
    """Producer for Redis Streams"""
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.redis_client = redis_client or RedisClient()
        self.settings = get_settings()

    async def send_message(self, stream: str, fields: Dict[str, Any], 
                          message_id: str = "*", maxlen: Optional[int] = None) -> str:
        """Send a message to a Redis Stream"""
        try:
            # Add timestamp if not present
            if "timestamp" not in fields:
                fields["timestamp"] = time.time()
            
            message_id = await self.redis_client.xadd(stream, fields, message_id, maxlen)
            logging.info(f"Sent message to stream {stream} with ID {message_id}")
            return message_id
        except Exception as e:
            logging.error(f"Failed to send message to stream {stream}: {e}")
            raise

    async def send_agent_request(self, conversation_id: str, message: str, **kwargs) -> str:
        """Send an agent request message"""
        stream = RedisStreamEnum.AGENT_REQUESTS.value.format(env=self.settings.environment)
        fields = {
            "conversation_id": conversation_id,
            "message": message,
            **kwargs
        }
        return await self.send_message(stream, fields)

    async def send_agent_response(self, conversation_id: str, response_data: Dict[str, Any], request_id: str) -> str:
        """Send an agent response message"""
        stream = f"{self.settings.environment}:agent:chat:responses:{conversation_id}-{request_id}"
        return await self.send_message(stream, response_data, maxlen=1000)  # Keep last 1000 responses


class RedisStreamsConsumer:
    """Consumer for Redis Streams with enhanced error handling"""

    def __init__(self, group: str, consumer_name: str, redis_client: Optional[RedisClient] = None):
        self.group = group
        self.consumer_name = consumer_name
        self.redis_client = redis_client or RedisClient()
        self.settings = get_settings()
        self._running = False
        self._retry_count = {}  # Track retry counts per message
        self._max_retries = 3
        self._backoff_base = 1  # Base backoff time in seconds

    async def ensure_consumer_group(self, stream: str, start_id: str = "0"):
        """Ensure consumer group exists for the stream"""
        try:
            await self.redis_client.xgroup_create(stream, self.group, id=start_id, mkstream=True)
            logging.info(f"Created consumer group {self.group} for stream {stream}")
        except Exception as e:
            if "BUSYGROUP" not in str(e):
                logging.error(f"Failed to create consumer group {self.group} for stream {stream}: {e}")
                raise
            else:
                logging.debug(f"Consumer group {self.group} already exists for stream {stream}")

    async def handle_processing_error(self, message: StreamMessage, error: Exception,
                                    manager: Optional['RedisStreamsManager'] = None) -> bool:
        """Handle processing errors with retry logic"""
        message_key = f"{message.stream}:{message.id}"
        retry_count = self._retry_count.get(message_key, 0)

        if retry_count >= self._max_retries:
            logging.error(f"Message {message.id} exceeded max retries ({self._max_retries}), moving to dead letter")

            if manager:
                await manager.move_to_dead_letter(message.stream, message, str(error))

            # Acknowledge the message to prevent infinite retries
            await self.acknowledge_message(message.stream, message.id)
            self._retry_count.pop(message_key, None)
            return False
        else:
            # Increment retry count and apply backoff
            self._retry_count[message_key] = retry_count + 1
            backoff_time = self._backoff_base * (2 ** retry_count)  # Exponential backoff

            logging.warning(f"Processing failed for message {message.id} (attempt {retry_count + 1}/{self._max_retries}), "
                          f"will retry after {backoff_time}s: {error}")

            await asyncio.sleep(backoff_time)
            return True  # Indicate retry should happen

    async def consume_messages(self, streams: List[str], count: Optional[int] = None,
                             block: Optional[int] = 1000) -> AsyncGenerator[StreamMessage, None]:
        """Consume messages from Redis Streams using consumer groups with enhanced error handling"""
        # Ensure consumer groups exist for all streams
        for stream in streams:
            await self.ensure_consumer_group(stream)

        self._running = True
        consecutive_errors = 0
        max_consecutive_errors = 5

        while self._running:
            try:
                # Build streams dict for XREADGROUP (use ">" to read new messages)
                streams_dict = {stream: ">" for stream in streams}

                result = await self.redis_client.xreadgroup(
                    self.group, self.consumer_name, streams_dict, count=count, block=block
                )

                # Reset error counter on successful read
                consecutive_errors = 0

                for stream_name, messages in result.items():
                    for message_id, fields in messages:
                        message = StreamMessage(
                            id=message_id,
                            fields=fields,
                            stream=stream_name,
                            timestamp=fields.get("timestamp")
                        )

                        # Clear retry count for successfully delivered messages
                        message_key = f"{message.stream}:{message.id}"
                        self._retry_count.pop(message_key, None)

                        yield message

            except Exception as e:
                consecutive_errors += 1
                logging.error(f"Error consuming messages (consecutive errors: {consecutive_errors}): {e}")

                if consecutive_errors >= max_consecutive_errors:
                    logging.critical(f"Too many consecutive errors ({consecutive_errors}), stopping consumer")
                    self._running = False
                    raise

                # Exponential backoff for consecutive errors
                backoff_time = min(30, 2 ** consecutive_errors)  # Cap at 30 seconds
                await asyncio.sleep(backoff_time)

    async def acknowledge_message(self, stream: str, message_id: str) -> bool:
        """Acknowledge a processed message"""
        try:
            result = await self.redis_client.xack(stream, self.group, message_id)
            return result > 0
        except Exception as e:
            logging.error(f"Failed to acknowledge message {message_id} in stream {stream}: {e}")
            return False

    async def consume_agent_requests(self, count: Optional[int] = None) -> AsyncGenerator[StreamMessage, None]:
        """Consume agent request messages"""
        stream = RedisStreamEnum.AGENT_REQUESTS.value.format(env=self.settings.environment)
        async for message in self.consume_messages([stream], count=count):
            yield message

    def stop(self):
        """Stop consuming messages"""
        self._running = False


class RedisStreamsResponseReader:
    """Simple reader for response streams (no consumer groups needed)"""
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.redis_client = redis_client or RedisClient()
        self.settings = get_settings()

    async def read_responses(self, conversation_id: str, last_id: str = "0", 
                           count: Optional[int] = None, block: Optional[int] = None) -> List[StreamMessage]:
        """Read response messages for a conversation"""
        stream = f"{self.settings.environment}:agent:responses:{conversation_id}"
        
        try:
            result = await self.redis_client.xread({stream: last_id}, count=count, block=block)
            
            messages = []
            if stream in result:
                for message_id, fields in result[stream]:
                    messages.append(StreamMessage(
                        id=message_id,
                        fields=fields,
                        stream=stream,
                        timestamp=fields.get("timestamp")
                    ))
            
            return messages
        except Exception as e:
            logging.error(f"Failed to read responses for conversation {conversation_id}: {e}")
            return []

    async def stream_responses(self, conversation_id: str, last_id: str = "$", 
                             block: int = 1000) -> AsyncGenerator[StreamMessage, None]:
        """Stream response messages for a conversation in real-time"""
        stream = f"{self.settings.environment}:agent:chat:responses:{conversation_id}"
        current_id = last_id
        
        while True:
            try:
                result = await self.redis_client.xread({stream: current_id}, count=1, block=block)
                
                if stream in result:
                    for message_id, fields in result[stream]:
                        current_id = message_id
                        yield StreamMessage(
                            id=message_id,
                            fields=fields,
                            stream=stream,
                            timestamp=fields.get("timestamp")
                        )
                        
            except Exception as e:
                logging.error(f"Error streaming responses for conversation {conversation_id}: {e}")
                await asyncio.sleep(1)


class RedisStreamsManager:
    """High-level manager for Redis Streams operations"""

    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.redis_client = redis_client or RedisClient()
        self.producer = RedisStreamsProducer(self.redis_client)
        self.settings = get_settings()

    def get_consumer(self, group: str, consumer_name: str) -> RedisStreamsConsumer:
        """Get a consumer instance"""
        return RedisStreamsConsumer(group, consumer_name, self.redis_client)

    def get_response_reader(self) -> RedisStreamsResponseReader:
        """Get a response reader instance"""
        return RedisStreamsResponseReader(self.redis_client)

    async def cleanup_old_streams(self, max_age_hours: int = 24):
        """Clean up old stream data"""
        # This would implement cleanup logic for old streams
        # For now, we'll just log the intent
        logging.info(f"Cleanup of streams older than {max_age_hours} hours would be performed here")

    async def handle_pending_messages(self, group: str, consumer_name: str, stream: str,
                                    max_retries: int = 3) -> int:
        """Handle pending messages that were not acknowledged"""
        try:
            # Get pending messages for this consumer
            pending_info = await self.redis_client.xpending_range(
                stream, group, min_id="-", max_id="+", count=100, consumer=consumer_name
            )

            processed_count = 0
            for message_info in pending_info:
                message_id = message_info['message_id']
                delivery_count = message_info['times_delivered']

                if delivery_count >= max_retries:
                    # Move to dead letter queue or acknowledge to prevent infinite retries
                    logging.warning(f"Message {message_id} exceeded max retries ({max_retries}), acknowledging")
                    await self.redis_client.xack(stream, group, message_id)
                    processed_count += 1
                else:
                    # Re-claim the message for processing
                    logging.info(f"Re-claiming pending message {message_id} (attempt {delivery_count + 1})")
                    await self.redis_client.xclaim(
                        stream, group, consumer_name, 60000, message_id  # 1 minute
                    )
                    processed_count += 1

            if processed_count > 0:
                logging.info(f"Processed {processed_count} pending messages for consumer {consumer_name}")

            return processed_count

        except Exception as e:
            logging.error(f"Error handling pending messages: {e}")
            return 0

    async def create_dead_letter_stream(self, original_stream: str) -> str:
        """Create a dead letter stream for failed messages"""
        dead_letter_stream = f"{original_stream}:dead_letter"

        try:
            # Ensure the dead letter stream exists
            await self.redis_client.xadd(dead_letter_stream, {"initialized": "true"}, maxlen=1)
            logging.info(f"Created dead letter stream: {dead_letter_stream}")
            return dead_letter_stream
        except Exception as e:
            logging.error(f"Failed to create dead letter stream {dead_letter_stream}: {e}")
            raise

    async def move_to_dead_letter(self, original_stream: str, message: StreamMessage,
                                error_reason: str = "processing_failed"):
        """Move a failed message to dead letter queue"""
        try:
            dead_letter_stream = await self.create_dead_letter_stream(original_stream)

            # Add error metadata to the message
            dead_letter_fields = {
                **message.fields,
                "original_stream": original_stream,
                "original_message_id": message.id,
                "error_reason": error_reason,
                "failed_at": time.time()
            }

            await self.redis_client.xadd(dead_letter_stream, dead_letter_fields)
            logging.info(f"Moved message {message.id} to dead letter stream {dead_letter_stream}")

        except Exception as e:
            logging.error(f"Failed to move message {message.id} to dead letter queue: {e}")

    async def get_stream_info(self, stream: str) -> Dict[str, Any]:
        """Get information about a stream"""
        try:
            info = await self.redis_client.xinfo_stream(stream)
            return {
                "length": info.get("length", 0),
                "first_entry": info.get("first-entry"),
                "last_entry": info.get("last-entry"),
                "groups": info.get("groups", 0)
            }
        except Exception as e:
            logging.error(f"Failed to get stream info for {stream}: {e}")
            return {}

    async def monitor_consumer_group_health(self, stream: str, group: str) -> Dict[str, Any]:
        """Monitor the health of a consumer group"""
        try:
            group_info = await self.redis_client.xinfo_groups(stream)

            for group_data in group_info:
                if group_data['name'] == group:
                    return {
                        "pending_messages": group_data.get("pending", 0),
                        "last_delivered_id": group_data.get("last-delivered-id"),
                        "consumers": group_data.get("consumers", 0)
                    }

            return {"error": f"Consumer group {group} not found"}

        except Exception as e:
            logging.error(f"Failed to get consumer group health for {group}: {e}")
            return {"error": str(e)}
