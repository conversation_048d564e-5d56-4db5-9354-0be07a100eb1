import json
from typing import Any, Dict, Optional

from app.shared.config.constants import SSEEventType

# Global storage for pending tool use
_pending_tool_use = None


def format_stream_chunk(chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    global _pending_tool_use

    # Handle init_event_loop
    if "init_event_loop" in chunk and chunk["init_event_loop"]:
        return {"type": SSEEventType.STREAM_START.value}

    # Ignore these events
    if (
        ("start" in chunk and chunk["start"])
        or ("start_event_loop" in chunk and chunk["start_event_loop"])
        or ("event" in chunk and "messageStart" in chunk["event"])
    ):
        return None

    # Handle contentBlockStart - check for tool use first
    if "event" in chunk and "contentBlockStart" in chunk["event"]:
        start_content = chunk["event"]["contentBlockStart"]["start"]
        if "toolUse" in start_content:
            tool_name = start_content["toolUse"]["name"]
            return {"type": SSEEventType.TOOL_USE_START.value, "tool_name": tool_name}
        else:
            return {"type": SSEEventType.MESSAGE_START.value, "message_type": "text"}

    # Handle contentBlockDelta - check for tool use first
    if "event" in chunk and "contentBlockDelta" in chunk["event"]:
        delta = chunk["event"]["contentBlockDelta"]["delta"]
        if "toolUse" in delta:
            tool_input = delta["toolUse"]["input"]
            return {"type": SSEEventType.TOOL_CHUNK.value, "tool_delta": tool_input}
        elif "text" in delta:
            delta_text = delta["text"]
            return {"type": SSEEventType.CHUNK.value, "delta": delta_text}

    # Ignore these events (including tool_use stop events)
    if "event" in chunk and (
        "contentBlockStop" in chunk["event"]
        or "messageStop" in chunk["event"]
        or "metadata" in chunk["event"]
    ):
        return None

    # Handle message with text content
    if "message" in chunk and "content" in chunk["message"]:
        content = chunk["message"]["content"]
        if isinstance(content, list) and len(content) > 0:
            if "text" in content[0]:
                return {
                    "type": SSEEventType.TEXT_RESULT.value,
                    "result": {"type": "text", "content": content[0]["text"]},
                    "db_save": True,
                }
            elif "toolUse" in content[0]:
                # Store tool use info for potential pairing with tool result
                _pending_tool_use = content[0]["toolUse"]
                return None
            elif "toolResult" in content[0]:
                # Combine with pending tool use
                if _pending_tool_use:
                    tool_result = content[0]["toolResult"]
                    result = {
                        "type": SSEEventType.TOOL_RESULT.value,
                        "result": json.dumps(
                            {
                                "type": "tool",
                                "name": _pending_tool_use["name"],
                                "tool_input": _pending_tool_use["input"],
                                "tool_output": (
                                    {
                                        "text": tool_result["content"][0].get(
                                            "text", ""
                                        ),
                                    }
                                    if tool_result.get("content")
                                    else {"type": "tool"}
                                ),
                            }
                        ),
                        "db_save": True,
                    }
                    _pending_tool_use = None  # Reset
                    return result
                return None

    # Handle AgentResult
    if "result" in chunk and hasattr(chunk["result"], "metrics"):
        usage = chunk["result"].metrics.accumulated_usage
        return {
            "type": SSEEventType.STREAM_END.value,
            "usage_info": {
                "inputTokens": usage.get("inputTokens", 0),
                "outputTokens": usage.get("outputTokens", 0),
                "totalTokens": usage.get("totalTokens", 0),
            },
        }

    # Keep existing logic
    if "data" in chunk or "delta" in chunk:
        return None
