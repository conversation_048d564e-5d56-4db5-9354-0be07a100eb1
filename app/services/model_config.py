import os
from typing import Optional
from strands.models.openai import OpenAIModel
from dotenv import load_dotenv

load_dotenv()

DEFAULT_PROVIDER = os.getenv("DEFAULT_PROVIDER", "openai")
DEFAULT_MODEL_NAME = os.getenv("DEFAULT_MODEL_NAME", "gpt-4o-mini")
DEFAULT_MAX_TOKENS = int(os.getenv("DEFAULT_MAX_TOKENS", "1000"))
DEFAULT_TEMPERATURE = float(os.getenv("DEFAULT_TEMPERATURE", "0.7"))


def get_model(provider: str, name: str, max_tokens: Optional[int] = None, temperature: Optional[float] = None, **kwargs) -> OpenAIModel:
    # Use OpenRouter for all providers
    api_key = os.getenv("OPENROUTER_API_KEY")
    base_url = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
    if not api_key:
        raise ValueError("OPENROUTER_API_KEY environment variable is required")

    # Use provider and name directly for OpenRouter model ID
    model_id = f"{provider}/{name}"

    client_args = {
        "api_key": api_key,
        "base_url": base_url,
    }

    params = {
        "max_tokens": max_tokens or DEFAULT_MAX_TOKENS,
        "temperature": temperature or DEFAULT_TEMPERATURE,
        **kwargs,
    }

    return OpenAIModel(
        client_args=client_args,
        model_id=model_id,
        params=params,
    )


def get_default_model() -> OpenAIModel:
    return get_model(DEFAULT_PROVIDER, DEFAULT_MODEL_NAME)


def create_custom_model(
    provider: str,
    name: str,
    max_tokens: Optional[int] = None,
    temperature: Optional[float] = None,
    **kwargs
) -> OpenAIModel:
    return get_model(provider, name, max_tokens, temperature, **kwargs)
