from app.helper.redis_client import RedisClient
from app.shared.config.base import get_settings
from app.shared.config.constants import RedisConsumerGroupEnum, RedisStreamEnum
from app.services.redis_streams import RedisStreamsManager
from app.agent.global_agent import run_agent_stream
from app.services.model_config import DEFAULT_PROVIDER, DEFAULT_MODEL_NAME
import logging
import time
import json
import uuid

settings = get_settings()


async def listen_event_from_redis_streams():
    """Listen for events from Redis Streams using consumer groups"""
    logging.info(f"Listening for events from Redis Streams: {int(time.time())}")

    # Initialize Redis Streams manager
    streams_manager = RedisStreamsManager()

    # Create a unique consumer name for this instance
    consumer_name = f"agent-worker-{uuid.uuid4().hex[:8]}"

    # Get consumer for agent requests
    consumer = streams_manager.get_consumer(
        group=RedisConsumerGroupEnum.AGENT_PROCESSORS.value,
        consumer_name=consumer_name
    )

    logging.info(f"Starting consumer {consumer_name} for group {RedisConsumerGroupEnum.AGENT_PROCESSORS.value}")

    try:
        # Handle any pending messages first
        await streams_manager.handle_pending_messages(
            group=RedisConsumerGroupEnum.AGENT_PROCESSORS.value,
            consumer_name=consumer_name,
            stream=RedisStreamEnum.AGENT_REQUESTS.value.format(env=settings.environment),
        )

        # Consume agent request messages
        async for message in consumer.consume_agent_requests(count=settings.redis.consumer_batch_size):
            logging.info(f"Received message from stream {message.stream}: ID={message.id}, timestamp={int(time.time())}")

            processing_successful = False
            retry_needed = True

            while retry_needed:
                try:
                    # Extract message data
                    conversation_id = message.fields.get("conversation_id")
                    request_id = message.fields.get("request_id")
                    user_message = message.fields.get("message")
                    model_data = message.fields.get("model", {})
                    attachments = message.fields.get("attachments", [])
                    logging.info(f"model_data: {model_data}")
                    provider = model_data.get("provider", DEFAULT_PROVIDER)
                    model_name = model_data.get("name", DEFAULT_MODEL_NAME)

                    logging.info(f"Using provider: {provider}, model: {model_name} for conversation {conversation_id}")

                    if not conversation_id or not user_message:
                        logging.warning(f"Invalid message format: {message.fields}")
                        await consumer.acknowledge_message(message.stream, message.id)
                        processing_successful = True
                        break

                    logging.info(f"Processing message for conversation {conversation_id}: {int(time.time())}")

                    # Process the user message with the agent
                    async for formatted_chunk in run_agent_stream(user_message, provider, model_name, attachments):
                        # Send the formatted chunk to the Redis Streams response stream
                        await streams_manager.producer.send_agent_response(
                            conversation_id=conversation_id,
                            response_data=formatted_chunk,
                            request_id=request_id
                        )

                    # Acknowledge the message after successful processing
                    ack_success = await consumer.acknowledge_message(message.stream, message.id)
                    if ack_success:
                        logging.info(f"Successfully processed and acknowledged message {message.id}")
                        processing_successful = True
                        retry_needed = False
                    else:
                        logging.warning(f"Failed to acknowledge message {message.id}")
                        retry_needed = False  # Don't retry ACK failures

                except Exception as e:
                    import traceback
                    logging.error(f"Error processing message {message.id}: {traceback.format_exc()}")
                    logging.error(f"Error details: {e}")

                    # Use enhanced error handling
                    retry_needed = await consumer.handle_processing_error(message, e, streams_manager)

                    if not retry_needed:
                        processing_successful = False
                        break

            if not processing_successful:
                logging.error(f"Failed to process message {message.id} after all retries")

    except Exception as e:
        logging.error(f"Fatal error in Redis Streams listener: {e}")
        raise
    finally:
        logging.info(f"Stopping Redis Streams consumer {consumer_name}")
        consumer.stop()


async def listen_event_from_redis_pubsub():
    """Listen for events from Redis Streams"""
    logging.info("Using Redis Streams implementation")
    await listen_event_from_redis_streams()
