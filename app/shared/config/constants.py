from enum import Enum


class RedisStreamEnum(str, Enum):
    """Redis Streams names for agent communication"""
    AGENT_REQUESTS = "{env}:agent:chat:requests"
    AGENT_RESPONSES = "{env}:agent:chat:responses:{conversation_id}"


class RedisConsumerGroupEnum(str, Enum):
    """Redis Streams consumer group names"""
    AGENT_PROCESSORS = "agent-processors"


class SSEEventType(str, Enum):
    STREAM_START = "stream_start"
    MESSAGE_START = "message_start"
    CHUNK = "chunk"
    TOOL_USE_START = "tool_use_start"
    TOOL_CHUNK = "tool_chunk"
    TOOL_RESULT = "tool_result"
    TEXT_RESULT = "text_result"
    STREAM_END = "stream_end"
