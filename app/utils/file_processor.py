import aiohttp
import asyncio
from typing import List, Dict, Tuple, Any


def get_image_format_from_mime_type(mime_type: str) -> str:
    """Determine image format from MIME type."""
    if mime_type == 'image/jpeg':
        return 'jpeg'
    elif mime_type == 'image/png':
        return 'png'
    elif mime_type == 'image/gif':
        return 'gif'
    else:
        return 'jpeg'  # default


async def fetch_gcs_file_bytes(url: str) -> bytes:
    """Fetch bytes from a GCS URL."""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.read()


async def fetch_multiple_gcs_files(attachments: List[Dict[str, Any]]) -> List[Tuple[bytes, str]]:
    """Fetch bytes and formats from multiple attachments concurrently."""
    if not attachments:
        return []
    tasks = [fetch_gcs_file_bytes(att['file_url']) for att in attachments]
    bytes_list = await asyncio.gather(*tasks)
    formats = [get_image_format_from_mime_type(att['file_type']) for att in attachments]
    return list(zip(bytes_list, formats))