from typing import Optional, List, Dict, Any
from strands_tools import calculator, current_time, image_reader
from app.agent.base_provider import Base<PERSON>rovider
from app.agent.tools.web_search import web_search
from app.services.model_config import get_model
from app.agent.tools.todo import todo_agent_tool


class GlobalAgent(BaseProvider):
    """
    Global agent provider that uses python_repl and calculator tools
    """

    def __init__(self):
        """
        Initialize the global agent with python_repl and calculator tools
        """
        super().__init__(
            tools=[current_time, calculator, web_search, todo_agent_tool, image_reader]
        )


global_agent = GlobalAgent()


async def run_agent_stream(
    message: str, provider: Optional[str] = None, model_name: Optional[str] = None, attachments: Optional[List[Dict[str, Any]]] = None
):
    """
    Generator function that yields formatted chunks for agent responses
    """
    from app.services.model_config import DEFAULT_PROVIDER, DEFAULT_MODEL_NAME

    provider = provider or DEFAULT_PROVIDER
    model_name = model_name or DEFAULT_MODEL_NAME
    model = get_model(provider, model_name)
    agent = BaseProvider(
        tools=[current_time, calculator, web_search, todo_agent_tool, image_reader],
        model=model,
    )
    async for chunk in agent.run_agent_stream(message, attachments):
        yield chunk


async def test_agent():
    """
    Test function to run the agent with user input and print chunks
    """
    user_input = input("Enter your message: ")
    print(f"Running agent with input: {user_input}")
    print("-" * 50)

    # result = run_agent(user_input)
    # print(result)
    # print(f"Total tokens: {result.metrics.accumulated_usage}")
    # print(f"Execution time: {sum(result.metrics.cycle_durations):.2f} seconds")
    # print(f"Tools used: {list(result.metrics.tool_metrics.keys())}")

    async for chunk in run_agent_stream(user_input):
        print(chunk)

    print("\n" + "-" * 50)
    print("Agent response completed")


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_agent())
