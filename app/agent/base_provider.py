from abc import ABC
from typing import List, Optional, Any, Dict
from app.utils.file_processor import fetch_multiple_gcs_files


class BaseProvider(ABC):
    """
    Base class for agent providers.
    Provides a common interface for agent initialization and streaming responses.
    """

    def __init__(self, tools=None, model=None):
        """
        Initialize the provider with tools and model configuration.
        Args:
            tools: List of tools to be used by the agent
            model: Model configuration for the agent
        """
        from strands import Agent
        from app.services.model_config import get_default_model

        self.model = model or get_default_model()
        self.agent = Agent(tools=tools or [], model=self.model)

    async def run_agent_stream(
        self, message: str, attachments: Optional[List[Dict[str, Any]]] = None
    ):
        """
        Generator function that yields formatted chunks for agent responses.
        Args:
            message: User message to be processed
            attachments: Optional list of attachment dicts with file_url and file_type

        Yields:
            Formatted event chunks
        """
        from app.services.stream_formatter import format_stream_chunk

        content: List[Any] = [{"text": message}]

        if attachments:
            image_data_list = await fetch_multiple_gcs_files(attachments)
            for image_bytes, format in image_data_list:
                content.append(
                    {
                        "image": {
                            "format": format,
                            "source": {"bytes": image_bytes},
                        }
                    }
                )

        response = self.agent.stream_async(content)
        async for event in response:
            formatted_event = format_stream_chunk(event)
            if formatted_event:
                yield formatted_event
