FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false \
    PYTHONPATH="/app:$PYTHONPATH"

# Add Poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    build-essential \
    git \
    ca-certificates \
    sudo \
    vim \
    nano \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --no-interaction --no-ansi --no-root --without dev

# Create directories for data persistence
RUN mkdir -p /app/data/chromadb /app/logs /app/uploads /app/.mem0

# Copy project files
COPY . .

# Set full permissions for all files (running as root)
RUN chmod -R 755 /app

# Expose port
EXPOSE 6000

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:6000/health || exit 1

# Run the application
CMD ["sh", "-c", "poetry run python -m app.main --mode server"]